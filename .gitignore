# Compiled files
mev/target/

# Cargo package files
**/*.rs.bk
Cargo.lock

# Backup files
*.swp
*.swo
*~

# IntelliJ/Rust plugin specific
.idea/
*.iml

# VS Code settings
.vscode/

# MacOS system files
.DS_Store

# Byebug history (if you're using it in tools)
/.byebug_history

# Logs and temporary files
*.log
*.tmp
*.temp
*.cache
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Node artifacts (if using JS bindings or tooling)
node_modules/
dist/
build/

# Test output
debug/
test_output/

# Coverage tools
coverage/
*.lcov
*.gcno
*.gcda
*.gcov

# Rust analyzer cache
target/
rust-project.json

# Environment files
.env


**/out/*
**/cache/*
**/lib/*

**/broadcast/*

**/logs/*
