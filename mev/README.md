# EVM MEV projects

## Setup

Create a .env file and set the following variables:

```bash
RPC_HTTPS_URL=
RPC_WSS_URL=
CHAIN_ID=999
# Private key for transaction signing
PRIVATE_KEY=0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80
# Simulator contract address
SIMULATOR_ADDRESS=******************************************
```

## Projects

### 1. Mempool monitoring system for EVM chains

This should work for all EVM compatible chains with the mempool API available.

Ethereum, BSC, Berachain, etc.

Run:

```bash
cargo run --release --bin mempool-monitor
```

```
Tx hash: 0xda2739146e60f5cc0bc55f6ecfce90754995b118a9466f2a7f9e0e69a9e40614

Tx hash: 0x22ba26731135744e5986b4b977555ca05ebc582aa396f994f5de009fbc39fec6
2025-01-02T11:04:41.105049Z  INFO mempool_monitor: V3: Ok(Log { address: ******************************************, data: Swap { sender: ******************************************, recipient: ******************************************, amount0: -************, amount1: 45000000000000000, sqrtPriceX96: 16890130550038774723541115646484, liquidity: 5290986765420913319, tick: 107248 } })
2025-01-02T11:04:41.105318Z  INFO mempool_monitor: Transfer: Ok(Log { address: ******************************************, data: Transfer { from: ******************************************, to: ******************************************, value: ************ } })
2025-01-02T11:04:41.105433Z  INFO mempool_monitor: Transfer: Ok(Log { address: 0xc02aaa39b223fe8d0a0e5c4f27ead9083c756cc2, data: Transfer { from: ******************************************, to: ******************************************, value: 45000000000000000 } })
2025-01-02T11:04:41.105531Z  INFO mempool_monitor: Transfer: Ok(Log { address: ******************************************, data: Transfer { from: ******************************************, to: 0x000000fee13a103a10d593b9ae06b3e05f2e7e1c, value: 2450744434 } })
2025-01-02T11:04:41.105625Z  INFO mempool_monitor: Transfer: Ok(Log { address: ******************************************, data: Transfer { from: ******************************************, to: ******************************************, value: 977847029536 } })

Tx hash: 0x01892e49a9c1ccb57635945bc469ddd02e182f6b4ebed2429ea77d30030158be
```

### 2. MEV LST arbitrage research

There are two different LST arbitrage strategies.

**Strategy #1**: Mint LST and sell on different venue for WETH

- WETH -deposit-> LST -swap-> WETH
- Buy: Deposit WETH into LST contract and mint LST
- Sell: Swap LST for WETH on different venues (Uniswap V3, Balancer V2, Curve V2)

**Strategy #2**: Buy LST and directly redeem WETH

- WETH -swap-> LST -redeem-> WETH
- Buy: Buy LST from different venue (flashloan is possible)
- Sell: Redeem WETH from LST contract

The strategies have been tested on the following contracts (contracts/src/Simulator.sol):

```bash
cd contracts
forge build
forge test --fork-url http://localhost:8545 --fork-block-number 18732930 --via-ir -vv
```

Run Rust script:

```bash
cargo run --release --bin lst-mev
```

```
2025-01-02T10:39:48.876042Z  INFO lst_mev: Target block number: 18732930
2025-01-02T10:39:50.272180Z ERROR simulator::traits::simulator: transfer_token reverted. gas_used=27718, output=0x08c379a0000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000024153000000000000000000000000000000000000000000000000000000000000
2025-01-02T10:39:50.272649Z  INFO lst_mev: amount_in=0, profit=0, took=758ms
2025-01-02T10:39:53.369634Z ERROR simulator::traits::simulator: transfer_token reverted. gas_used=274706, output=0x
2025-01-02T10:39:53.370173Z  INFO lst_mev: amount_in=100000000000000000000, profit=0, took=3097ms
2025-01-02T10:39:58.768452Z ERROR simulator::traits::simulator: transfer_token reverted. gas_used=449999, output=0x
2025-01-02T10:39:58.769361Z  INFO lst_mev: amount_in=200000000000000000000, profit=0, took=5399ms
2025-01-02T10:40:04.177210Z ERROR simulator::traits::simulator: transfer_token reverted. gas_used=449999, output=0x
2025-01-02T10:40:04.177485Z  INFO lst_mev: amount_in=300000000000000000000, profit=0, took=5408ms
2025-01-02T10:40:09.577390Z ERROR simulator::traits::simulator: transfer_token reverted. gas_used=449999, output=0x
2025-01-02T10:40:09.578368Z  INFO lst_mev: amount_in=400000000000000000000, profit=0, took=5400ms
2025-01-02T10:40:14.981048Z ERROR simulator::traits::simulator: transfer_token reverted. gas_used=449999, output=0x
2025-01-02T10:40:14.982157Z  INFO lst_mev: amount_in=500000000000000000000, profit=0, took=5403ms
2025-01-02T10:40:20.426880Z ERROR simulator::traits::simulator: transfer_token reverted. gas_used=449999, output=0x
2025-01-02T10:40:20.427472Z  INFO lst_mev: amount_in=600000000000000000000, profit=0, took=5445ms
2025-01-02T10:40:25.829888Z ERROR simulator::traits::simulator: transfer_token reverted. gas_used=449999, output=0x
2025-01-02T10:40:25.830212Z  INFO lst_mev: amount_in=700000000000000000000, profit=0, took=5402ms
2025-01-02T10:40:31.278723Z ERROR simulator::traits::simulator: transfer_token reverted. gas_used=449999, output=0x
2025-01-02T10:40:31.279187Z  INFO lst_mev: amount_in=800000000000000000000, profit=0, took=5449ms
2025-01-02T10:40:36.681143Z ERROR simulator::traits::simulator: transfer_token reverted. gas_used=449999, output=0x
2025-01-02T10:40:36.682200Z  INFO lst_mev: amount_in=900000000000000000000, profit=0, took=5403ms
2025-01-02T10:40:42.102395Z ERROR simulator::traits::simulator: transfer_token reverted. gas_used=449999, output=0x
2025-01-02T10:40:42.103234Z  INFO lst_mev: amount_in=1000000000000000000000, profit=0, took=5421ms
2025-01-02T10:40:42.811384Z ERROR simulator::traits::simulator: transfer_token reverted. gas_used=27718, output=0x08c379a0000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000024153000000000000000000000000000000000000000000000000000000000000
2025-01-02T10:40:42.812477Z  INFO lst_mev: amount_in=0, profit=0, took=709ms
2025-01-02T10:40:45.371553Z  INFO lst_mev: amount_in=10000000000000000000, profit=51759955692787022, took=2558ms
2025-01-02T10:40:47.944242Z  INFO lst_mev: amount_in=20000000000000000000, profit=85564146063761637, took=2572ms
2025-01-02T10:40:50.479827Z  INFO lst_mev: amount_in=30000000000000000000, profit=101460640575642009, took=2535ms
2025-01-02T10:40:53.040741Z  INFO lst_mev: amount_in=40000000000000000000, profit=99497337261482228, took=2560ms
2025-01-02T10:40:55.665126Z  INFO lst_mev: amount_in=50000000000000000000, profit=79721963488201519, took=2624ms
2025-01-02T10:40:58.226992Z  INFO lst_mev: amount_in=60000000000000000000, profit=42182076716036289, took=2561ms
2025-01-02T10:41:00.771354Z ERROR simulator::traits::simulator: transfer_token reverted. gas_used=210548, output=0x
2025-01-02T10:41:00.771775Z  INFO lst_mev: amount_in=70000000000000000000, profit=0, took=2544ms
2025-01-02T10:41:07.421154Z ERROR simulator::traits::simulator: transfer_token reverted. gas_used=210548, output=0x
2025-01-02T10:41:07.422260Z  INFO lst_mev: amount_in=80000000000000000000, profit=0, took=6650ms
2025-01-02T10:41:10.275527Z ERROR simulator::traits::simulator: transfer_token reverted. gas_used=210508, output=0x
2025-01-02T10:41:10.276169Z  INFO lst_mev: amount_in=90000000000000000000, profit=0, took=2853ms
2025-01-02T10:41:13.352218Z ERROR simulator::traits::simulator: transfer_token reverted. gas_used=274706, output=0x
2025-01-02T10:41:13.352725Z  INFO lst_mev: amount_in=100000000000000000000, profit=0, took=3076ms
2025-01-02T10:41:15.894471Z  INFO lst_mev: amount_in=20000000000000000000, profit=85564146063761637, took=2541ms
2025-01-02T10:41:18.421215Z  INFO lst_mev: amount_in=22000000000000000000, profit=90174525434070175, took=2526ms
2025-01-02T10:41:20.942218Z  INFO lst_mev: amount_in=24000000000000000000, profit=94068981249958115, took=2520ms
2025-01-02T10:41:23.466565Z  INFO lst_mev: amount_in=26000000000000000000, profit=97247896968753875, took=2524ms
2025-01-02T10:41:26.098962Z  INFO lst_mev: amount_in=28000000000000000000, profit=99711655773988905, took=2632ms
2025-01-02T10:41:43.678804Z  INFO lst_mev: amount_in=30000000000000000000, profit=101460640575642009, took=17580ms
2025-01-02T10:41:46.261869Z  INFO lst_mev: amount_in=32000000000000000000, profit=102495234010383417, took=2582ms
2025-01-02T10:41:48.831844Z  INFO lst_mev: amount_in=34000000000000000000, profit=102815818441818585, took=2569ms
2025-01-02T10:41:51.448207Z  INFO lst_mev: amount_in=36000000000000000000, profit=102422775960731740, took=2616ms
2025-01-02T10:41:54.000841Z  INFO lst_mev: amount_in=38000000000000000000, profit=101316488385329168, took=2552ms
2025-01-02T10:41:56.552372Z  INFO lst_mev: amount_in=40000000000000000000, profit=99497337261482228, took=2551ms
2025-01-02T10:41:59.079354Z  INFO lst_mev: amount_in=32000000000000000000, profit=102495234010383417, took=2526ms
2025-01-02T10:42:01.617871Z  INFO lst_mev: amount_in=32400000000000000000, profit=102616459386102771, took=2538ms
2025-01-02T10:42:10.563430Z  INFO lst_mev: amount_in=32800000000000000000, profit=102709127460161738, took=8945ms
2025-01-02T10:42:13.080826Z  INFO lst_mev: amount_in=33200000000000000000, profit=102773241289722716, took=2517ms
2025-01-02T10:42:15.635795Z  INFO lst_mev: amount_in=33600000000000000000, profit=102808803931511744, took=2554ms
2025-01-02T10:42:18.156779Z  INFO lst_mev: amount_in=34000000000000000000, profit=102815818441818585, took=2520ms
2025-01-02T10:42:20.670691Z  INFO lst_mev: amount_in=34400000000000000000, profit=102794287876496794, took=2513ms
2025-01-02T10:42:23.209864Z  INFO lst_mev: amount_in=34800000000000000000, profit=102744215290963804, took=2539ms
2025-01-02T10:42:25.717200Z  INFO lst_mev: amount_in=35200000000000000000, profit=102665603740201003, took=2507ms
2025-01-02T10:42:28.394149Z  INFO lst_mev: amount_in=35600000000000000000, profit=102558456278753807, took=2676ms
2025-01-02T10:42:30.945808Z  INFO lst_mev: amount_in=36000000000000000000, profit=102422775960731740, took=2551ms
2025-01-02T10:42:33.863319Z  INFO lst_mev: amount_in=33600000000000000000, profit=102808803931511744, took=2917ms
2025-01-02T10:42:36.391940Z  INFO lst_mev: amount_in=33680000000000000000, profit=102812490586298093, took=2528ms
2025-01-02T10:42:38.917716Z  INFO lst_mev: amount_in=33760000000000000000, profit=102815035340274773, took=2525ms
2025-01-02T10:42:41.443063Z  INFO lst_mev: amount_in=33840000000000000000, profit=102816438217889313, took=2525ms
2025-01-02T10:42:43.966183Z  INFO lst_mev: amount_in=33920000000000000000, profit=102816699243588540, took=2523ms
2025-01-02T10:42:46.521395Z  INFO lst_mev: amount_in=34000000000000000000, profit=102815818441818585, took=2555ms
2025-01-02T10:42:49.047994Z  INFO lst_mev: amount_in=34080000000000000000, profit=102813795837024880, took=2526ms
2025-01-02T10:42:51.562487Z  INFO lst_mev: amount_in=34160000000000000000, profit=102810631453652159, took=2514ms
2025-01-02T10:42:54.107006Z  INFO lst_mev: amount_in=34240000000000000000, profit=102806325316144462, took=2544ms
2025-01-02T10:42:56.623710Z  INFO lst_mev: amount_in=34320000000000000000, profit=102800877448945126, took=2516ms
2025-01-02T10:42:59.214992Z  INFO lst_mev: amount_in=34400000000000000000, profit=102794287876496794, took=2591ms
2025-01-02T10:43:01.756678Z  INFO lst_mev: amount_in=33840000000000000000, profit=102816438217889313, took=2541ms
2025-01-02T10:43:04.352131Z  INFO lst_mev: amount_in=33856000000000000000, profit=102816581770400096, took=2595ms
2025-01-02T10:43:06.913458Z  INFO lst_mev: amount_in=33872000000000000000, profit=102816679649029843, took=2561ms
2025-01-02T10:43:09.465551Z  INFO lst_mev: amount_in=33888000000000000000, profit=102816731853974120, took=2551ms
2025-01-02T10:43:12.005174Z  INFO lst_mev: amount_in=33904000000000000000, profit=102816738385428495, took=2539ms
2025-01-02T10:43:14.529674Z  INFO lst_mev: amount_in=33920000000000000000, profit=102816699243588540, took=2524ms
2025-01-02T10:43:17.058747Z  INFO lst_mev: amount_in=33936000000000000000, profit=102816614428649820, took=2529ms
2025-01-02T10:43:19.576308Z  INFO lst_mev: amount_in=33952000000000000000, profit=102816483940807900, took=2517ms
2025-01-02T10:43:22.100193Z  INFO lst_mev: amount_in=33968000000000000000, profit=102816307780258345, took=2523ms
2025-01-02T10:43:24.647652Z  INFO lst_mev: amount_in=33984000000000000000, profit=102816085947196720, took=2547ms
2025-01-02T10:43:27.215085Z  INFO lst_mev: amount_in=34000000000000000000, profit=102815818441818585, took=2567ms
2025-01-02T10:43:29.779698Z  INFO lst_mev: amount_in=33888000000000000000, profit=102816731853974120, took=2564ms
2025-01-02T10:43:32.318523Z  INFO lst_mev: amount_in=33891200000000000000, profit=102816736814137928, took=2538ms
2025-01-02T10:43:34.839785Z  INFO lst_mev: amount_in=33894400000000000000, profit=102816739947363706, took=2521ms
2025-01-02T10:43:37.393912Z  INFO lst_mev: amount_in=33897600000000000000, profit=102816741253653017, took=2553ms
2025-01-02T10:43:39.927115Z  INFO lst_mev: amount_in=33900800000000000000, profit=102816740733007425, took=2533ms
2025-01-02T10:43:42.479784Z  INFO lst_mev: amount_in=33904000000000000000, profit=102816738385428495, took=2552ms
2025-01-02T10:43:45.099726Z  INFO lst_mev: amount_in=33907200000000000000, profit=102816734210917793, took=2619ms
2025-01-02T10:43:47.649808Z  INFO lst_mev: amount_in=33910400000000000000, profit=102816728209476881, took=2550ms
2025-01-02T10:43:50.270009Z  INFO lst_mev: amount_in=33913600000000000000, profit=102816720381107326, took=2620ms
2025-01-02T10:43:52.781355Z  INFO lst_mev: amount_in=33916800000000000000, profit=102816710725810690, took=2511ms
2025-01-02T10:43:55.309825Z  INFO lst_mev: amount_in=33920000000000000000, profit=102816699243588540, took=2528ms
2025-01-02T10:43:57.953424Z  INFO lst_mev: amount_in=33894400000000000000, profit=102816739947363706, took=2643ms
2025-01-02T10:44:00.482888Z  INFO lst_mev: amount_in=33895040000000000000, profit=102816740354776435, took=2529ms
2025-01-02T10:44:07.530011Z  INFO lst_mev: amount_in=33895680000000000000, profit=102816740689111718, took=7047ms
2025-01-02T10:44:10.079693Z  INFO lst_mev: amount_in=33896320000000000000, profit=102816740950369568, took=2549ms
2025-01-02T10:44:12.612519Z  INFO lst_mev: amount_in=33896960000000000000, profit=102816741138549995, took=2532ms
2025-01-02T10:44:15.138110Z  INFO lst_mev: amount_in=33897600000000000000, profit=102816741253653017, took=2525ms
2025-01-02T10:44:17.661694Z  INFO lst_mev: amount_in=33898240000000000000, profit=102816741295678641, took=2523ms
2025-01-02T10:44:20.209114Z  INFO lst_mev: amount_in=33898880000000000000, profit=102816741264626881, took=2547ms
2025-01-02T10:44:22.759357Z  INFO lst_mev: amount_in=33899520000000000000, profit=102816741160497750, took=2550ms
2025-01-02T10:44:25.307550Z  INFO lst_mev: amount_in=33900160000000000000, profit=102816740983291260, took=2548ms
2025-01-02T10:44:41.694183Z  INFO lst_mev: amount_in=33900800000000000000, profit=102816740733007425, took=16386ms
2025-01-02T10:44:44.212556Z  INFO lst_mev: amount_in=33897600000000000000, profit=102816741253653017, took=2518ms
2025-01-02T10:44:46.741539Z  INFO lst_mev: amount_in=33897728000000000000, profit=102816741267904332, took=2528ms
2025-01-02T10:44:49.259094Z  INFO lst_mev: amount_in=33897856000000000000, profit=102816741279232552, took=2517ms
2025-01-02T10:44:51.778752Z  INFO lst_mev: amount_in=33897984000000000000, profit=102816741287637678, took=2519ms
2025-01-02T10:44:54.366425Z  INFO lst_mev: amount_in=33898112000000000000, profit=102816741293119706, took=2587ms
2025-01-02T10:44:56.907789Z  INFO lst_mev: amount_in=33898240000000000000, profit=102816741295678641, took=2541ms
2025-01-02T10:44:59.451519Z  INFO lst_mev: amount_in=33898368000000000000, profit=102816741295314479, took=2543ms
2025-01-02T10:45:01.984355Z  INFO lst_mev: amount_in=33898496000000000000, profit=102816741292027222, took=2532ms
2025-01-02T10:45:04.527087Z  INFO lst_mev: amount_in=33898624000000000000, profit=102816741285816869, took=2542ms
2025-01-02T10:45:07.076429Z  INFO lst_mev: amount_in=33898752000000000000, profit=102816741276683422, took=2549ms
2025-01-02T10:45:09.622929Z  INFO lst_mev: amount_in=33898880000000000000, profit=102816741264626881, took=2546ms
2025-01-02T10:45:09.623075Z  INFO lst_mev: Optimized: Optimized { optimized_in: 33898240000000000000, optimized_out: 102816741295678641 }
2025-01-02T10:45:09.623128Z  INFO lst_mev: Optimized amount in: 33898240000000000000
2025-01-02T10:45:09.623160Z  INFO lst_mev: Optimized profit: 102816741295678641
```

Optimized amount in: 33.89824 ETH
Optimized profit: 0.1028 ETH

### 3. Foundry Fork HyperEVM
Hyperliquid RPC URLS: https://hyperliquid.gitbook.io/hyperliquid-docs/for-developers/hyperevm#mainnet


Fork HyperEVM locally. Set bigger gas limit
```bash
anvil --fork-url https://rpc.hyperliquid.xyz/evm --block-gas-limit 50000000
```

Deploy Simulator contract
```bash
forge script script/DeploySimulator.s.sol:DeploySimulator \
  --rpc-url http://127.0.0.1:8545 \
  --private-key 0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80 \
  --broadcast \
  --gas-limit 2800000 \
  -vvvv
```

Env vars:
```bash
SIMULATOR_ADDRESS=******************************************
RPC_HTTPS_URL=http://127.0.0.1:8545
CHAIN_ID=999
PRIVATE_KEY=0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80
```

Run mempool-monitor
```bash
cargo run --release --bin mempool-monitor
```

Get address stHYPE balance
```bash
cast call ****************************************** "balanceOf(address)(uint256)" ******************************************

cast call ****************************************** "balanceOf(address)(uint256)" ******************************************
cast call ****************************************** "balanceOf(address)(uint256)" ******************************************
cast call ****************************************** "balanceOf(address)(uint256)" 0x1613beB3B2C4f22Ee086B2b38C1476A3cE7f78E8
cast call ****************************************** "balanceOf(address)(uint256)" ******************************************
```

****************************************** - 0th anvil address


Send native token to stHYPE holder: https://purrsec.com/address/******************************************/token-holders </br>
Required so when we sent stHYPE from the unlocked account, it has enough balance to pay for the tx
```bash
cast send ****************************************** \
  --value 0.1ether \
  --from ****************************************** \
  --rpc-url http://127.0.0.1:8545 \
  --unlocked
```

Send stHYPE from unlocked holder to 0th anvil address, so it has stHYPE to perform swap
```bash
cast rpc anvil_impersonateAccount ******************************************

cast send ****************************************** \
  "transfer(address,uint256)(bool)" \
  ****************************************** 10000000000000000000000 \
  --rpc-url http://127.0.0.1:8545 \
  --from ****************************************** \
  --unlocked \
  --gas-limit 2000000
```

Get pool reserves
```bash
cast call ****************************************** "getReserves()(uint112,uint112,uint32)" \
  --rpc-url http://127.0.0.1:8545
```

Approve stHYPE for hyperswap pool
```bash
cast send ****************************************** \
  "approve(address,uint256)(bool)" \
  ****************************************** \
  20000034225227453932460 \
  --rpc-url http://127.0.0.1:8545 \
  --from ****************************************** \
  --unlocked
```

Send stHYPE to pool to disbalance it
```bash
cast send ****************************************** \
  "transfer(address,uint256)" \
  ****************************************** \
  1000000000000000000000 \
  --from ****************************************** \
  --unlocked \
  --rpc-url http://127.0.0.1:8545
```

Perform swap
```bash
cast send ****************************************** \
  "swap(uint256,uint256,address,bytes)" \
  10000000000000000000 0 \
  ****************************************** \
  0x \
  --rpc-url http://127.0.0.1:8545 \
  --from ****************************************** \
  --unlocked
```

Other:

Simulate transaction
```bash
cast run 0xa763243295570c4d6016bc5683a552a537e40156f63c17c029443fc5926b2053 \
  --rpc-url http://127.0.0.1:8545
```

Deposit HYPE for WHYPE
```
cast send ****************************************** "deposit()" \
  --value 1ether \
  --from ****************************************** \
  --rpc-url http://127.0.0.1:8545 \
  --unlocked
```

Get tx trace:
```bash
cast run 0xd36f83840ab87780943f07e70a68a6f20e85d534ea11b354e265df89db8752dd --rpc-url http://localhost:8545
```
