// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";

abstract contract Withdrawable is Ownable {
    using SafeERC20 for IERC20;

    constructor(address initialOwner) Ownable(initialOwner) {}

    receive() external payable {}

    /// @dev Withdraws ETH or ERC20 tokens from the contract.
    /// @param token Address of the token to withdraw. Use address(0) for native ETH.
    /// @param amount Amount to withdraw.
    function withdrawTokens(address token, uint256 amount) external onlyOwner {
        if (token == address(0)) {
            // Withdraw native ETH
            require(address(this).balance >= amount, "Insufficient ETH balance");
            (bool success, ) = owner().call{value: amount}("");
            require(success, "ETH transfer failed");
        } else {
            // Withdraw ERC20 tokens
            IERC20(token).safeTransfer(owner(), amount);
        }
    }
}
