// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";

import "../interfaces/IValantisRouter.sol";
import { Withdrawable } from "../Withdrawable.sol";

contract ValantisSwapper is Withdrawable {
    using SafeERC20 for IERC20;

    address public immutable router;
    address public immutable HYPE;
    address public constant HYPE_BRIDGE = 0x2222222222222222222222222222222222222222;

    constructor(address _router, address _hype) Withdrawable(msg.sender) {
        router = _router;
        HYPE = _hype;
    }

    function swapAndBridge(
        address stableToken, // feUSD or USDXL
        uint amountIn,
        uint amountOutMin
    ) external onlyOwner {
        IERC20(stableToken).approve(router, amountIn);

        address[] memory path = new address[](2);
        path[0] = stableToken;
        path[1] = HYPE;

        // IValantisRouter(router).swapExactTokensForTokens( // todo check if this is the correct Valantis swap interface https://docs.valantis.xyz/deploy-and-build#arbitrum
        //     amountIn,
        //     amountOutMin,
        //     path,
        //     address(this),
        //     block.timestamp + 300
        // );
        // todo use HyperSwapV3 

        uint hypeBalance = IERC20(HYPE).balanceOf(address(this));
        require(hypeBalance > 0, "No HYPE received");

        IERC20(HYPE).safeTransfer(HYPE_BRIDGE, hypeBalance); // todo send to EOA and bridge from it instead
    }
}
