// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "./interfaces/IERC20.sol";
import "./interfaces/IERC4626.sol";
import "./interfaces/IUniswapV3Pool.sol";
import "./interfaces/IBalancerV2Vault.sol";
import "./interfaces/ICurveV2Pool.sol";
import "./interfaces/IUniswapV2Callee.sol";
import "./interfaces/IHyperswapPair.sol";
import "./interfaces/IValantisPool.sol";

import "./DataConstructor.sol";
import { Withdrawable } from "./Withdrawable.sol";

contract Simulator is
    DataConstructor,
    Withdrawable(msg.sender),
    IFlashLoanRecipient,
    IUniswapV2Callee
{
    // https://docs.valantis.xyz/deploy-and-build#hyperevm
    address constant valantisPool = ******************************************;

    constructor() {}

    error UnauthorizedCaller(address caller);
    error InvalidSellType(uint256 sellType);
    error TradeNotProfitable(int256 profit);

    // =====================
    // Arbitrage Scenario #1
    // this is profitable if the sell price is higher than the deposit price
    // 1. Buy: deposit into LST (mint)
    // 2. Sell: sell LST from different venue
    // WETH -deposit-> LST -swap-> WETH
    // =====================

    function flashloanLstArbitrage(address lst, address tokenIn, uint256 amountIn, bytes memory sellData) public {
        IBalancerV2Vault vaultContract = IBalancerV2Vault( // todo change address to hyperliquid one https://docs.hyperlend.finance/developer-documentation/core-pools/flash-loans
        ******************************************);

        IERC20[] memory tokens = new IERC20[](1);
        tokens[0] = IERC20(tokenIn);

        uint256[] memory amounts = new uint256[](1);
        amounts[0] = amountIn;

        bytes memory fullData = abi.encodePacked(abi.encode(lst), sellData);

        vaultContract.flashLoan(IFlashLoanRecipient(address(this)), tokens, amounts, fullData);
    }

    function receiveFlashLoan(
        IERC20[] memory tokens,
        uint256[] memory amounts,
        uint256[] memory, // feeAmounts, unused
        bytes memory data
    ) external {
        address BALANCER_VAULT = ******************************************;
        if (msg.sender != BALANCER_VAULT) revert UnauthorizedCaller(msg.sender);

        // Get initial state
        address borrowedToken = address(tokens[0]);
        uint256 initialBalance = amounts[0];

        // Decode base parameters
        (address lst, uint256 sellType) = abi.decode(data, (address, uint256));

        // Step 1: Deposit borrowed tokens into LST
        lstDeposit(lst, borrowedToken, initialBalance);
        uint256 lstBalance = IERC20(lst).balanceOf(address(this));

        // Step 2: Sell LST tokens based on sell type
        _executeSellStrategy(data, sellType, lstBalance);

        // Step 3: Calculate and log profit
        uint256 finalBalance = IERC20(borrowedToken).balanceOf(address(this));
        int256 profit = int256(finalBalance) - int256(initialBalance);

        if (profit < 0) {
            revert TradeNotProfitable(profit);
        }

        // Step 4: Repay loan
        IERC20(borrowedToken).transfer(BALANCER_VAULT, initialBalance);
    }

    function _executeSellStrategy(bytes memory data, uint256 sellType, uint256 amount) private {
        if (sellType == 0) {
            (,, address pool, bool zeroForOne) = abi.decode(data, (address, uint256, address, bool));
            uniswapV3Swap(pool, zeroForOne, amount);
        } else if (sellType == 1) {
            (,, bytes32 poolId, address tokenIn, address tokenOut) =
                abi.decode(data, (address, uint256, bytes32, address, address));
            balancerV2Swap(poolId, tokenIn, tokenOut, amount);
        } else if (sellType == 2) {
            (,, address pool, uint256 tokenInIdx, uint256 tokenOutIdx) =
                abi.decode(data, (address, uint256, address, uint256, uint256));
            curveV2Swap(pool, tokenInIdx, tokenOutIdx, amount);
        } else {
            revert InvalidSellType(sellType);
        }
    }

    // =====================
    // Arbitrage Scanario #2
    // 1. Buy: loan LST from Hyperswap V2/V3 using flashswap
    // 2. Sell: withdraw WHYPE directly from Valantis Sovereign Pool
    // WHYPE -swap-> LST -redeem-> WHYPE
    // WHYPE -> stHYPE
    // =====================
    // V2

    struct FlashCallbackData {
        address token0;
        address token1;
        uint256 amountIn;
        bool zeroForOne;
        address pool;
    }

    function flashswapLstArbitrageV2(
        address pool,
        bool zeroForOne, // true: token0 -> token1, false: token1 -> token0
        uint256 amountIn
    ) public {
        address token0 = IHyperswapPair(pool).token0();
        address token1 = IHyperswapPair(pool).token1();

        //                               WHYPE
        address tokenIn = zeroForOne ? token0 : token1;

        // get expected amountOut
        uint256 amountOut = IHyperswapPair(pool).getAmountOut(amountIn, tokenIn);

        // Determine which token is going out
        uint256 amount0Out = zeroForOne ? 0 : amountOut;
        uint256 amount1Out = zeroForOne ? amountOut : 0;

        // Initiate flash swap — the data param triggers the callback
        IHyperswapPair(pool).swap(
            amount0Out, // WHYPE
            amount1Out, // stHYPE
            address(this),
            abi.encode(
                FlashCallbackData({
                    token0: token0,
                    token1: token1,
                    amountIn: amountIn,
                    zeroForOne: zeroForOne,
                    pool: pool
                })
            )
        );
    }

    function uniswapV2Call(address sender, uint256 amount0, uint256 amount1, bytes calldata data) external {
        FlashCallbackData memory decoded = abi.decode(data, (FlashCallbackData));

        // amountOut -> swap at valantis for 1:1 ratio https://docs.valantis.xyz/staked-amm/ratio-fee
        // amountOUT should be in stHYPE

        // checks
        require(msg.sender == decoded.pool, "Caller must be Hyperswap pool");
        require(sender == address(this), "Sender must be this contract");

        uint256 amountIn = decoded.amountIn;
        address tokenInHs = decoded.zeroForOne ? decoded.token0 : decoded.token1; // WHYPE
        address tokenOutHs = decoded.zeroForOne ? decoded.token1 : decoded.token0; // stHYPE

        // Determine which token amount was borrowed (should match amountIn)
        uint256 borrowedAmount = amount0 > 0 ? amount0 : amount1; // borrowed stHYPE
        require(borrowedAmount > 0, "Borrowed amount mismatch");

        // Approve Valantis pool to spend the borrowed tokenIn
        IERC20(tokenOutHs).approve(address(valantisPool), borrowedAmount);

        SovereignPoolSwapParams memory swapParams =
            constructValantisSwapData(decoded.zeroForOne, borrowedAmount, address(this), tokenInHs, tokenOutHs);

        // Swap on Valantis: tokenIn (stHYPE) -> tokenOut (HYPE)
        uint256 amountOut = IValantisPool(valantisPool).swap(swapParams);

        // Calculate amount to repay Hyperswap pool (amountIn + fee)
        uint256 fee = ((amountIn * 3) / 997) + 1; // ~0.3% fee, Uniswap V2 style
        uint256 amountToRepay = amountIn + fee;

        require(amountOut >= amountToRepay, "Insufficient output from Valantis swap");

        // Repay the Hyperswap flash swap loan with tokenOut
        IERC20(tokenInHs).transfer(decoded.pool, amountToRepay);
    }

    // V3

    function flashswapLstArbitrageV3(address pool, bool zeroForOne, uint256 amountIn) public {
        IUniswapV3Pool v3Pool = IUniswapV3Pool(pool);

        address token0 = v3Pool.token0();
        address token1 = v3Pool.token1();

        (address tokenIn, address tokenOut) = zeroForOne ? (token0, token1) : (token1, token0);

        uint160 sqrtPriceLimitX96 = zeroForOne ? ********** : 1461446703485210103287273052203988822378723970341;

        bytes memory data = abi.encode(pool, tokenIn, tokenOut);

        v3Pool.swap(address(this), zeroForOne, int256(amountIn), sqrtPriceLimitX96, data);
    }

    function uniswapV3SwapCallback(int256 amount0Delta, int256 amount1Delta, bytes calldata data) external {
        // no callback protection in place
        // use only for testing/simulation purposes
        uint256 amountIn = uint256(amount0Delta > 0 ? amount0Delta : amount1Delta);

        if (data.length == 64) {
            // regular v3 swap
            (address pool, address tokenIn) = abi.decode(data, (address, address));
            IERC20(tokenIn).transfer(pool, amountIn);
        } else {
            // flashswap
            (address pool, address tokenIn, address tokenOut) = abi.decode(data, (address, address, address));

            IERC20 lst20 = IERC20(tokenOut);
            IERC4626 lst4626 = IERC4626(tokenOut);

            uint256 lstBalance = lst20.balanceOf(address(this));
            uint256 shares = lst4626.convertToShares(lstBalance);

            // get back WETH
            lst4626.redeem(shares, address(this), address(this));

            // repay loan
            IERC20(tokenIn).transfer(pool, amountIn);
        }
    }

    // ==========
    // Other
    // ==========

    function lstDeposit(address lst, address tokenIn, uint256 amountIn) public {
        IERC4626 lstContract = IERC4626(lst);
        require(lstContract.asset() == tokenIn);

        IERC20(tokenIn).approve(lst, amountIn);
        lstContract.deposit(amountIn, address(this));
    }

    // sellType = 0
    function uniswapV3Swap(address pool, bool zeroForOne, uint256 amountIn) public {
        IUniswapV3Pool v3Pool = IUniswapV3Pool(pool);

        address tokenIn = zeroForOne ? v3Pool.token0() : v3Pool.token1();

        uint160 sqrtPriceLimitX96 = zeroForOne ? ********** : 1461446703485210103287273052203988822378723970341;

        bytes memory data = abi.encode(pool, tokenIn);

        v3Pool.swap(address(this), zeroForOne, int256(amountIn), sqrtPriceLimitX96, data);
    }

    // sellType = 1
    function balancerV2Swap(bytes32 poolId, address tokenIn, address tokenOut, uint256 amountIn) public {
        address vault = ******************************************;

        IERC20 tokenInContract = IERC20(tokenIn);
        tokenInContract.approve(vault, 0xFFFFFFFFFFFFFFFFFFFFFFFF);

        IBalancerV2Vault vaultContract = IBalancerV2Vault(vault);

        vaultContract.swap(
            IBalancerV2Vault.SingleSwap(
                poolId, IBalancerV2Vault.SwapKind.GIVEN_IN, IAsset(tokenIn), IAsset(tokenOut), amountIn, new bytes(0)
            ),
            IBalancerV2Vault.FundManagement(address(this), false, payable(address(this)), false),
            0,
            11533977638873292903519766084849772071321814878804040558617845282038221897728
        );
    }

    // sellType = 2
    function curveV2Swap(address pool, uint256 tokenInIdx, uint256 tokenOutIdx, uint256 amountIn) public {
        ICurveV2Pool v2Pool = ICurveV2Pool(pool);

        address tokenIn = v2Pool.coins(tokenInIdx);

        IERC20 tokenInContract = IERC20(tokenIn);
        tokenInContract.approve(pool, amountIn);

        v2Pool.exchange(tokenInIdx, tokenOutIdx, amountIn, 0);
    }
}
