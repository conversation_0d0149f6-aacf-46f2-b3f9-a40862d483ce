// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "./interfaces/IStexAmm.sol";
import { SovereignPoolSwapContextData, SovereignPoolSwapParams } from "./interfaces/IValantisPool.sol";

contract DataConstructor {
    // Address of the Valantis Sovereign Pool, always the entry-point for swaps
    // https://docs.valantis.xyz/staked-amm/integration
    address constant STEX_AMM_SOVEREIGN_POOL = 0x5365b6EF09253C7aBc0A9286eC578A9f4B413B7D;

    // Address of STEX AMM Liquidity Module bound to `STEX_AMM_SOVEREIGN_POOL`.
    // This is the main AMM module which handles all swap related pricing logic.
    address constant STEX_AMM = 0x39694eFF3b02248929120c73F90347013Aec834d;

    // https://docs.valantis.xyz/hot/swap/amm-swap
    function constructValantisSwapData(
        bool isZeroToOne,
        uint256 amountIn,
        address recipient,
        address tokenOut,
        address tokenIn
    ) public returns (SovereignPoolSwapParams memory swapData) {
        uint256 amountOutExpected = IStexAmm(STEX_AMM).getAmountOut(tokenIn, amountIn, false);

        SovereignPoolSwapContextData memory data;

        swapData = SovereignPoolSwapParams({
            isSwapCallback: false,
            isZeroToOne: isZeroToOne,
            amountIn: amountIn,
            amountOutMin: amountOutExpected - 10,
            deadline: block.timestamp + 10,
            recipient: recipient,
            swapTokenOut: tokenOut,
            swapContext: data
        });
    }
}
