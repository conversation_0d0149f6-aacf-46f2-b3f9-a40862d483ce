[workspace]
members  = ["crates/*", "bins/*"]
resolver = "2"

[workspace.package]
version = "0.1.0"

[workspace.dependencies]
anyhow             = "1.0.94"
const_format       = "0.2.32"
csv                = "1.1"
derivative         = "2.2.0"
dotenv             = "0.15.0"
evm-fork-db        = { path = "./crates/evm-fork-db" }
eyre               = "0.6.12"
futures            = "0.3.30"
futures-util       = "0.3"
itertools          = "0.11.0"
mempool-monitor    = { path = "./bins/mempool-monitor" }
parking_lot        = "0.12"
serde              = "1.0"
serde_json         = "1.0"
shared             = { path = "./crates/shared" }
simulator          = { path = "./crates/simulator" }
tx_sender          = { path = "./crates/tx_sender" }
pool_data          = { path = "./crates/pool_data" }
thiserror          = "1"
tokio              = { version = "1.39.3", features = ["full"] }
tracing            = "0.1.40"
tracing-appender   = "0.2.3"
tracing-subscriber = { version = "0.3.18", features = ["env-filter", "fmt", "json"] }
url                = "2"

# Alloy
alloy = { version = "0.8", features = [
    "eips",
    "full",
    "hyper",
    "json-rpc",
    "node-bindings",
    "rpc-client",
    "rpc-types-debug",
    "rpc-types-trace",
    "signer-aws",
    "signer-gcp",
    "signer-keystore",
    "signer-ledger",
    "signer-mnemonic",
    "signer-trezor",
    "signer-yubihsm",
] }

alloy-consensus = { version = "0.8", default-features = false }
alloy-network = "0.8"
alloy-primitives = { version = "0.8", features = ["rand"] }
alloy-provider = { version = "0.8", features = [
    "reqwest",
    "pubsub",
    "ws",
    "ipc",
    "debug-api",
    "anvil-node",
    "anvil-api",
] }
alloy-rpc-client = "0.8"
alloy-rpc-types = "0.8"
alloy-rpc-types-eth = { version = "0.8" }
alloy-rpc-types-trace = { version = "0.8" }
alloy-serde = { version = "0.8", default-features = false }
alloy-sol-types = "0.8"
alloy-transport = { version = "0.8", default-features = false }
alloy-transport-http = "0.8"

# Revm
revm = { version = "18.0.0", default-features = false, features = ["std", "serde"] }

# Reth
reth               = { git = "https://github.com/paradigmxyz/reth" }
reth-chainspec     = { git = "https://github.com/paradigmxyz/reth" }
reth-db            = { git = "https://github.com/paradigmxyz/reth" }
reth-node-ethereum = { git = "https://github.com/paradigmxyz/reth" }
reth-node-types    = { git = "https://github.com/paradigmxyz/reth" }
reth-provider      = { git = "https://github.com/paradigmxyz/reth" }

# Foundry
foundry-evm      = { git = "https://github.com/foundry-rs/foundry" }
foundry-evm-core = { git = "https://github.com/foundry-rs/foundry" }

[profile.release]
codegen-units   = 1
debug           = true
opt-level       = 3
overflow-checks = true
