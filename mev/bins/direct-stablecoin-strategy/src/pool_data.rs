use std::collections::HashMap;
use once_cell::sync::Lazy;
use alloy_primitives::Address;

// Static list of pool addresses
pub static POOL_ADDRESSES: Lazy<Vec<Address>> = Lazy::new(|| {
    vec![
        "0xc62f47203F2C1038A528293c6662d1Db0599Ce94" // HyperswapV3 USDXL/HYPE pool
            .parse()
            .unwrap(),
        "0xc53F8EA77FaD9a896440675d4E21ABe50F40321f" // HyperswapV3 feUSD/HYPE pool
            .parse()
            .unwrap(),
    ]
});

pub static POOL_TOKEN_MAPPING: Lazy<HashMap<Address, (Address, Address)>> = Lazy::new(|| {
    let mut m = HashMap::new();

    m.insert(
        "0xc62f47203F2C1038A528293c6662d1Db0599Ce94".parse().unwrap(),
        (
            "0x5555555555555555555555555555555555555555".parse().unwrap(),
            "0xca79db4B49f608eF54a5CB813FbEd3a6387bC645".parse().unwrap(),
        ),
    );

    m.insert(
        "0xc53F8EA77FaD9a896440675d4E21ABe50F40321f".parse().unwrap(),
        (
            "0x02c6a2fA58cC01A18B8D9E00eA48d65E4dF26c70".parse().unwrap(),
            "0x5555555555555555555555555555555555555555".parse().unwrap(),
        ),
    );

    m
});
