pub(crate) mod abis;
pub(crate) mod pool_data;

use tokio::time::sleep;
use std::time::Duration;
use hyperliquid_rust_sdk::{
    InfoClient, BaseUrl
};
use alloy_primitives::{U160, U256, Address};
use alloy::providers::Provider;
use alloy_rpc_types::BlockNumberOrTag;
use alloy_rpc_types::TransactionTrait;


use abis::IHyperswapV3Pool;
use pool_data::{POOL_ADDRESSES, POOL_TOKEN_MAPPING};

use shared::utils::{get_env, get_ws_provider};

use bigdecimal::BigDecimal;
use num_bigint::{BigUint, ToBigInt};
use std::str::FromStr;
use anyhow::Result;
// Short 10,000 HYPE at $1.05 on L1
// Buy 10,000 HYPE at $1.00 on EVM
// Bridge to L1 and use it to close the short

#[tokio::main]
async fn main() {
    dotenv::dotenv().ok();

    let rpc_wss_url = get_env("RPC_WSS_URL");

    let provider = get_ws_provider(&rpc_wss_url).await;

    let sleep_duration_ms = get_env("SLEEP_DURATION_MS").parse::<u64>().unwrap();

    let _min_price_diff = get_env("MIN_PRICE_DIFFERENCE");

    // Create a pool contract instance for each pool
    let pools = POOL_ADDRESSES
        .iter()
        .map(|addr| IHyperswapV3Pool::new(*addr, provider.clone()))
        .collect::<Vec<_>>();

    loop {
        // Get Hypercore price here using rust SDK
        let hypercore_price = get_hype_price_usdc().await;

        for pool in pools.iter() {
            match pool.slot0().call().await {
                Ok(result) => {
                    println!("Pool {}: sqrtPriceX96 = {}, tick = {}", pool.address(), result.sqrtPriceX96, result.tick);
                    let (token0, token1) = POOL_TOKEN_MAPPING
                        .get(pool.address())
                        .expect("Pool address not found in POOL_TOKEN_MAPPING");

                    println!("Token0: {}, Token1: {}", token0, token1);

                    // calculate price from sqrtPriceX96
                    let pool_price = calculate_price(result.sqrtPriceX96, *token0);

                    println!("     Pool price: {}", pool_price);
                    println!("Hypercore price: {}", hypercore_price);

                    if pool_price > hypercore_price { continue; }

                    // compare with Hypercore and Pool prices
                    let price_diff_percentage = get_percentage_difference(&pool_price, &hypercore_price);
                    println!("Price diff %: {}", price_diff_percentage);

                    // if price_diff_percentage < BigDecimal::from_str(&min_price_diff.clone()).unwrap() { continue; }

                    // todo calculate arbitrage opportunity

                    // todo get mempool txs max gas
                    match get_mempool_txs_max_gas1().await {
                        Ok((max_priority_fee, max_fee)) => {
                            println!("Max priority fee: {} wei, Max fee: {} wei", max_priority_fee, max_fee);
                        }
                        Err(e) => {
                            eprintln!("Error getting mempool gas data: {:?}", e);
                            continue;
                        }
                    }

                    // todo Send arbitrage TXs https://alloy.rs/examples/transactions/send_private_transaction#example-send_private_transaction
                }
                Err(e) => {
                    eprintln!("Error calling slot0 on pool {}: {:?}", pool.address(), e);
                }
            }

        }
        sleep(Duration::from_millis(sleep_duration_ms)).await;
    }

    // Arbitrage TXs
    // ~~1. Open Short on Hypercore: -> use rust sdk~~
    // ~~2. Swap stablecoin for HYPE on HyperSwap and send HYPE to EOA -> need contract~~
    // 3. Bridge HYPE from HyperEVM to Hypercore -> only EOA can bridge
    // 4. Close Short with bridged HYPE-> use rust sdk

    // Test bridge time -> Slow, but with the right price divergence percentage can minimize risk
}

// todo Try to find sellers ask price for better calc mid price = (ask + bid) / 2
// Worse case it has a 0.02% spread -> 1-2% reduction in profit
async fn get_hype_price_usdc() -> BigDecimal {
    // Create info client for public data
    let info_client = InfoClient::new(None, Some(BaseUrl::Mainnet)).await.unwrap();

    let all_mids = info_client.all_mids().await.unwrap();

    let hype_price = all_mids.get("HYPE").unwrap();

    hype_price.parse::<BigDecimal>().unwrap()
}

// Calculates Pool price from sqrtPriceX96 based on which token is WHYPE
fn calculate_price(_sqrt_price_x96: U160, token0: Address) -> BigDecimal {
    let sqrt_price_x96 = BigUint::from_str(&_sqrt_price_x96.to_string()).unwrap();
    let numerator = &sqrt_price_x96 * &sqrt_price_x96;
    let denominator: BigUint = BigUint::from(1u64) << 192;

    let numerator = BigDecimal::from(numerator.to_bigint().unwrap());
    let denominator = BigDecimal::from(denominator.to_bigint().unwrap());

    // If token0 is WHYPE, then price = numerator / denominator
    let price = if token0 == ("0x5555555555555555555555555555555555555555".parse::<Address>().unwrap()) {
        numerator / denominator
    } else {
        denominator / numerator
    };

    price.with_scale(3)
}

fn get_percentage_difference(price1: &BigDecimal, price2: &BigDecimal) -> BigDecimal {
    let difference = (price2 - price1).abs();
    let hundred = BigDecimal::from(100);

    // (|price2 - price1| / price1) * 100
    let diff = (&difference / price1) * &hundred;

    diff.with_scale(3)
}


// max_fee_per_gas - total cap willing to pay
// max_priority_fee_per_gas - miner tip
async fn get_mempool_txs_max_gas() -> Result<(U256, U256)> {
    let provider = get_ws_provider(get_env("RPC_WSS_URL").as_str()).await;

    // Pending block = transactions in the mempool waiting to get picked up by validators.
    // These are NOT yet validated or included in any block. They're just sitting in the mempool waiting for selection.
    let pending_block = provider
        .get_block(alloy_rpc_types::BlockId::Number(BlockNumberOrTag::Pending), true.into())
        .await?;

    let mut max_priority_fee = 0u128;
    let mut max_fee = 0u128;

    println!("Pending block number: {}", alloy_rpc_types::BlockId::Number(BlockNumberOrTag::Pending));

    if let Some(block) = pending_block {
        if let Some(transactions) = block.transactions.as_transactions() {
            // Clone transactions to avoid lifetime issues
            let tx_list: Vec<_> = transactions.iter().cloned().collect();
            for tx in tx_list {
                // Get the max priority fee per gas (returns Option<u128>)
                if let Some(priority_fee) = TransactionTrait::max_priority_fee_per_gas(&tx) {
                    if priority_fee > max_priority_fee {
                        max_priority_fee = priority_fee;
                    }
                }

                // Get the max fee per gas (returns u128)
                let fee = TransactionTrait::max_fee_per_gas(&tx);
                if fee > max_fee {
                    max_fee = fee;
                }

                println!("TX: {:?}, Priority Fee: {:?} wei, Max Fee: {} wei",
                    tx.inner.tx_hash(),
                    TransactionTrait::max_priority_fee_per_gas(&tx),
                    fee
                );
            }
        }
    }

    Ok((U256::from(max_priority_fee), U256::from(max_fee)))
}

use alloy_provider::{ProviderBuilder, WsConnect};

use futures::StreamExt;
use std::sync::Arc;
use tokio::time::{timeout, Duration as TokioDuration};

async fn get_mempool_txs_max_gas1() -> Result<(U256, U256)> {
    println!("Starting get_mempool_txs_max_gas1...");

    // Use the same RPC endpoint as the rest of the application
    let rpc_wss_url = get_env("RPC_WSS_URL");
    println!("Connecting to WebSocket: {}", rpc_wss_url);

    // Connect using WebSocket with pubsub support
    let provider = Arc::new(
        ProviderBuilder::new()
            .on_ws(WsConnect::new(&rpc_wss_url))
            .await?
    );

    println!("WebSocket connection established, subscribing to pending transactions...");

    // Subscribe to pending tx **bodies** directly
    let mut stream = provider
        .subscribe_full_pending_transactions()
        .await?
        .into_stream();

    let mut max_priority_fee = U256::ZERO;
    let mut max_fee = U256::ZERO;
    let mut tx_count = 0;
    let max_txs_to_process = 10; // Limit to prevent infinite loop

    println!("Subscription successful, waiting for transactions...");

    // Add timeout to prevent infinite waiting
    let timeout_duration = TokioDuration::from_secs(30); // 30 second timeout

    while let Some(tx) = timeout(timeout_duration, stream.next()).await.unwrap_or(None) {
        tx_count += 1;
        println!("Processing transaction #{}", tx_count);

        // tx: alloy_rpc_types_eth::TransactionResponse
        if let Some(prio) = tx.max_priority_fee_per_gas() {
            let prio_u256 = U256::from(prio);
            if prio_u256 > max_priority_fee {
                max_priority_fee = prio_u256;
            }
        }

        let fee = tx.max_fee_per_gas();
        let fee_u256 = U256::from(fee);
        if fee_u256 > max_fee {
            max_fee = fee_u256;
        }

        println!(
            "TX #{} prio: {:?}, fee: {:?}",
            tx_count, tx.max_priority_fee_per_gas(), tx.max_fee_per_gas()
        );

        // Break after processing a limited number of transactions
        if tx_count >= max_txs_to_process {
            println!("Processed {} transactions, stopping...", max_txs_to_process);
            break;
        }
    }

    if tx_count == 0 {
        println!("No transactions received within timeout period. This might indicate:");
        println!("1. The WebSocket endpoint doesn't support pending transaction subscriptions");
        println!("2. There are no pending transactions at the moment");
        println!("3. The RPC endpoint is not an Ethereum-compatible chain");
    }

    println!("Finished processing transactions. Max priority fee: {}, Max fee: {}", max_priority_fee, max_fee);
    Ok((max_priority_fee, max_fee))
}
