
```env
SLEEP_DURATION_MS=2000
RPC_WSS_URL
MIN_PRICE_DIFFERENCE
```


// Arbitrage TXs
<pre>
// 1. Open Short on Hypercore: -> use rust sdk
    Option 1: Unleveraged (Spot) Sale
        Already own HYPE on L1 and sell it at $1.05
            ✅ No borrowing risk
            ✅ Simple logic
            ? Requires HYPE inventory upfront
            ? Limits arbitrage size to what you already own

    Option 2: Leveraged Short (Margin Trade)
        Borrow HYPE from Hyperliquid, sell at $1.05, then buy it back cheaper on EVM
            ✅ Amplifies profits
            ✅ Capital-efficient (use 2x–10x leverage)
            ❌ Carries liquidation risk if price rises before you close - low chance
            ❌ Must repay borrowed HYPE by bridging back from EVM
</pre>
// 2. Swap stablecoin for HYPE on HyperSwap and send HYPE to EOA -> need contract

// 3.  ~~Bridge HYPE from HyperEVM to Hypercore -> only EOA can bridge~~

// 4.  ~~Close Short with bridged HYPE (option 2) -> use rust sdk-~~ No need HC is a derivatives exchange and uses USDC and other assets as margin. No need to repay PnL is settled in USDC

Vulnerabilities ?
MEV disbalancing the HyperSwap pool (TX 2) ->


Short-a на Hypercore не е нужно да е с leverage. Може и spot. За да е по бързо би било добре да държим HYPE на Hypercore. Даже не е нужно да bridge-ваме, защото HC е derivatives exchange и използва USDC (можеи други асети) като margin. PnL-а се получава директно в USDC. Няма нужда да затварям позицията като плащаме HYPE.

В случая оставаме с 2 транзакции:
1. Buy on HyperEVM Hyperswap
2. Sell on Hypercore

За да не изпуснем арбитража е най добре да държим HYPE на Hypercore за да не се налага да bridge-ваме. Slippage ще ни пази да не губим пари, ако ни FR и разбалансират pool-a.

За да не ни FR трябва да НЕ ни знаят адресите от които пускаме транзакциите. В случая двете транзакции НЕ са обвързани една с друга - два отделни трейда са. Трудно да ни засечат но след като ще имаме стейкинг и ще се знае какво прави бота определено ще има хора които ще ни кампят.

Solutions:
Staking contract is a huge point of failure against MEV.
Privacy / Decentralization -> Inverted

1. Using different addresses each time - send funds from staking contract to CEX and back to a different address that the bot generates randomly and uses to make the trades.
Cons:
    - seems centralized
Pros:
    - completely anonymous and un-FR-able
    - queue based unstaking
    - Gas and fees - inefficient

2. Delegate call + a lot of gas to execute instantly, before captured by MEV bots
Cons:
    - somewhat traceable

Pros:
    - kinda decentralized

3. Multisig or MPC Address Rotation

Pros:
    - Kinda Decentralized
    - Anonymous

Cons:
    - Latency
    - Complex to implement
    - MEV tracable

4. Redeploy for every trade - same as using different address
Pros:
    - completely anonymous and un-FR-able
Cons:
    - seems centralized
    - Very gas - inefficient

5. Just-In-Time (JIT) Transaction Broadcasting ?
Broadcast the transaction just before a block is created and send enough gas so it can be executed instantly.
HyperEvm ~0.89-1 sec blocktime https://hyperevm-explorer.vercel.app/ send tx 600–800ms after last block so there are only 100ms to execute 
Hypercore 0.07 sec -> Not FR-able ? Bots cantreact so fast 


За node са ни нужни 10,000 HYPE ~$400_000 - това го изключваме като решение

TL DR: На най-абстрактно ниво трябва да изберем между децентрализация (incinetive for users to stake) и анонимност (un-FR-ableness). Ако сме анонимни user-ите няма да имат incentive to stake защото средствата ще са в анонимен адрес през повечето време. Ако НЕ са в анонимен адрес ще ни FR-ват и няма смисъл.