use std::time::Instant;

use anyhow::Result;
use revm::primitives::U256;
use simulator::evm::EVM;
use simulator::traits::SimulatorContract;
use tracing::info;

use alloy::{
    primitives::Address,
    providers::ProviderBuilder,
    rpc::types::BlockId,
};

use crate::abi::IHyperswapPair;

#[derive(Debu<PERSON>, Clone)]
pub struct Optimized {
    pub optimized_in: u128,
    pub optimized_out: u128,
}

async fn simulate(
    rpc_https_url: &str,
    target_block_number: u64,
    weth: Address,
    target_uniswap_v3_pool: Address,
    zfo: bool,
    amount_in: u128,
) -> Result<u128> {
    let owner = Address::random();

    let mut evm = EVM::new(
        &rpc_https_url,
        None,
        None,
        target_block_number,
        weth,
        owner,
        U256::from(10_u64.pow(18)), // 1 ETH
    )
    .await;

    let balance_before = evm.get_token_balance(weth, evm.simulator()).unwrap().0;

    // Perform flashswap arbitrage.
    evm.flashswap_lst_arbitrage(target_uniswap_v3_pool, zfo, U256::from(amount_in))?;

    let balance_after = evm.get_token_balance(weth, evm.simulator()).unwrap().0;

    let profit = balance_after.saturating_sub(balance_before);

    match profit.try_into() {
        Ok(profit_u64) => Ok(profit_u64),
        Err(_) => {
            info!("Profit too large for u128, returning 0");
            Ok(0)
        }
    }
}

// Quadratic search for optimal amount_in.
pub async fn optimize_arbitrage(
    rpc_https_url: &str,
    target_block_number: u64,
    weth: Address,
    target_uniswap_v3_pool: Address,
    zfo: bool,
) -> Result<Optimized> {
    let intervals = 10;
    let tolerance = 10_u128.pow(15); // 0.001 ETH
    let ceiling = 10_u128.pow(18) * 1000; // 1000 ETH

    let mut min_amount_in = 0; // 0 ETH
    let mut max_amount_in = ceiling;
    let mut optimized_in = 0;
    let mut max_profit = 0;

    while max_amount_in - min_amount_in > tolerance {
        let step = (max_amount_in - min_amount_in) / intervals;
        if step == 0 {
            break;
        }

        let mut best_local_profit = 0;
        let mut best_local_amount_in = min_amount_in;

        for i in 0..=intervals {
            let amount_in = std::cmp::min(min_amount_in + i * step, ceiling);

            let s = Instant::now();
            let profit = simulate(
                rpc_https_url,
                target_block_number,
                weth,
                target_uniswap_v3_pool,
                zfo,
                amount_in,
            )
            .await
            .unwrap_or(0);

            let took = s.elapsed().as_millis();
            info!("amount_in={amount_in}, profit={profit}, took={took}ms");

            if profit > best_local_profit {
                best_local_profit = profit;
                best_local_amount_in = amount_in;
            }

            if profit > max_profit {
                max_profit = profit;
                optimized_in = amount_in;
            }

            if amount_in == ceiling {
                break;
            }
        }

        if best_local_amount_in == min_amount_in {
            min_amount_in = best_local_amount_in;
            max_amount_in = std::cmp::min(best_local_amount_in + step, ceiling);
        } else if best_local_amount_in == max_amount_in {
            min_amount_in = max_amount_in.saturating_sub(step);
            // NB: Intentionally leave max_amount_in unchanged.
        } else {
            min_amount_in = best_local_amount_in.saturating_sub(step);
            max_amount_in = std::cmp::min(best_local_amount_in + step, ceiling);
        }
    }

    let optimized_in: u128 = optimized_in.try_into().unwrap_or(0);
    let optimized_out: u128 = max_profit.try_into().unwrap_or(0);

    Ok(Optimized { optimized_in, optimized_out })
}

pub async fn optimize_arbitrage_v2_int_calc(
    rpc_https_url: &str,
    target_block_number: u64,
    whype: Address,
    target_hyperswap_v2_pool: Address,
    zfo: bool,
) -> Result<Optimized> {
    // get provider
    let provider = ProviderBuilder::new().on_http(rpc_https_url.parse()?);
    let pair_contract = IHyperswapPair::new(target_hyperswap_v2_pool, &provider);

    let block_id = BlockId::Number(target_block_number.into());

    // Fetch pool reserves at target block
    let reserves_call = pair_contract
        .getReserves()
        .block(block_id);

    // Add debug logging
    info!("Calling getReserves on pool: {}", target_hyperswap_v2_pool);

    let reserves_result = match reserves_call.call().await {
        Ok(result) => result,
        Err(e) => {
            info!("getReserves call failed: {}", e);
            return Err(anyhow::anyhow!("Failed to get reserves: {}", e));
        }
    };

    info!("Reserves: {:?}", reserves_result);

    let reserve0 = reserves_result.reserve0;
    let reserve1 = reserves_result.reserve1;

    // Get token addresses to determine order
    let token0_call = pair_contract.token0().block(block_id);
    let token1_call = pair_contract.token1().block(block_id);

    let token0 = match token0_call.call().await {
        Ok(result) => result._0,
        Err(e) => {
            info!("token0 call failed: {}", e);
            return Err(anyhow::anyhow!("Failed to get token0: {}", e));
        }
    };

    let token1 = match token1_call.call().await {
        Ok(result) => result._0,
        Err(e) => {
            info!("token1 call failed: {}", e);
            return Err(anyhow::anyhow!("Failed to get token1: {}", e));
        }
    };

    info!("token0: {}, token1: {}, whype: {}", token0, token1, whype);

    // Determine which reserve corresponds to whype token
    let (reserve_whype, reserve_other) = if token0 == whype {
        info!("WHYPE is token0");
        (reserve0.to::<u128>(), reserve1.to::<u128>())
    } else if token1 == whype {
        info!("WHYPE is token1");
        (reserve1.to::<u128>(), reserve0.to::<u128>())
    } else {
        return Err(anyhow::anyhow!("WHYPE token not found in the pool"));
    };

    info!("reserve_whype: {}, reserve_other: {}", reserve_whype, reserve_other);

    // Quadratic search for optimal amount
    let optimal_result = if zfo {
        // Zero for One: selling whype for other token
        quadratic_search_zfo(reserve_whype, reserve_other)
    } else {
        // One for Zero: buying whype with other token
        quadratic_search_otz(reserve_whype, reserve_other)
    };

    info!("Optimal result: {:?}", optimal_result);

    Ok(optimal_result)
}

fn quadratic_search_zfo(reserve_whype: u128, reserve_other: u128) -> Optimized {
    let max_input = reserve_whype / 4; // Reasonable upper bound
    let mut left = 1u128;
    let mut right = max_input;
    let mut best_result = Optimized { optimized_in: 0, optimized_out: 0 };

    // Quadratic search with golden ratio approximation
    while right - left > 3 {
        let mid1 = left + (right - left) / 3;
        let mid2 = right - (right - left) / 3;

        let profit1 = calculate_profit_zfo(mid1, reserve_whype, reserve_other);
        let profit2 = calculate_profit_zfo(mid2, reserve_whype, reserve_other);

        if profit1 > profit2 {
            right = mid2;
            if profit1 > best_result.optimized_out {
                best_result.optimized_in = mid1;
                best_result.optimized_out = profit1;
            }
        } else {
            left = mid1;
            if profit2 > best_result.optimized_out {
                best_result.optimized_in = mid2;
                best_result.optimized_out = profit2;
            }
        }
    }

    // Check remaining values
    for amount in left..=right {
        let profit = calculate_profit_zfo(amount, reserve_whype, reserve_other);
        if profit > best_result.optimized_out {
            best_result.optimized_in = amount;
            best_result.optimized_out = profit;
        }
    }

    best_result
}

fn quadratic_search_otz(reserve_whype: u128, reserve_other: u128) -> Optimized {
    let max_input = reserve_other / 4; // Reasonable upper bound
    let mut left = 1u128;
    let mut right = max_input;
    let mut best_result = Optimized { optimized_in: 0, optimized_out: 0 };

    // Quadratic search with golden ratio approximation
    while right - left > 3 {
        let mid1 = left + (right - left) / 3;
        let mid2 = right - (right - left) / 3;

        let profit1 = calculate_profit_otz(mid1, reserve_whype, reserve_other);
        let profit2 = calculate_profit_otz(mid2, reserve_whype, reserve_other);

        if profit1 > profit2 {
            right = mid2;
            if profit1 > best_result.optimized_out {
                best_result.optimized_in = mid1;
                best_result.optimized_out = profit1;
            }
        } else {
            left = mid1;
            if profit2 > best_result.optimized_out {
                best_result.optimized_in = mid2;
                best_result.optimized_out = profit2;
            }
        }
    }

    // Check remaining values
    for amount in left..=right {
        let profit = calculate_profit_otz(amount, reserve_whype, reserve_other);
        if profit > best_result.optimized_out {
            best_result.optimized_in = amount;
            best_result.optimized_out = profit;
        }
    }

    best_result
}

fn calculate_profit_zfo(amount_in: u128, reserve_whype: u128, reserve_other: u128) -> u128 {
    if amount_in >= reserve_whype {
        return 0;
    }

    // Uniswap V2 formula: amount_out = (amount_in * 997 * reserve_out) / (reserve_in * 1000 + amount_in * 997)
    let amount_in_with_fee = U256::from(amount_in) * U256::from(997u32);
    let numerator = U256::from(amount_in_with_fee) * U256::from(reserve_other);
    let denominator = U256::from(reserve_whype) * U256::from(1000u32) + amount_in_with_fee;

    if denominator.is_zero() {
        return 0;
    }

    let amount_out = numerator / denominator;

    // Simple profit calculation - in real arbitrage you'd compare with other DEX prices
    // This assumes some baseline profit calculation
    if amount_out > U256::from(amount_in) {
        amount_out.to::<u128>() - amount_in
    } else {
        0
    }
}

fn calculate_profit_otz(amount_in: u128, reserve_whype: u128, reserve_other: u128) -> u128 {
    if amount_in >= reserve_other {
        return 0;
    }

    // Uniswap V2 formula: amount_out = (amount_in * 997 * reserve_out) / (reserve_in * 1000 + amount_in * 997)
    let amount_in_with_fee = amount_in * 997;
    let numerator = amount_in_with_fee * reserve_whype;
    let denominator = reserve_other * 1000 + amount_in_with_fee;

    if denominator == 0 {
        return 0;
    }

    let amount_out = numerator / denominator;

    // Simple profit calculation - in real arbitrage you'd compare with other DEX prices
    // This assumes some baseline profit calculation
    if amount_out > amount_in {
        amount_out - amount_in
    } else {
        0
    }
}
