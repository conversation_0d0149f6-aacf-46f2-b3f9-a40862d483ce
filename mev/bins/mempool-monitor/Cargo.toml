[package]
edition = "2021"
name    = "mempool-monitor"
version = "0.1.0"

[dependencies]
alloy                 = { workspace = true }
alloy-primitives      = { workspace = true }
alloy-provider        = { workspace = true }
alloy-rpc-types       = { workspace = true, features = ["debug", "trace"] }
alloy-rpc-types-eth   = { workspace = true }
alloy-rpc-types-trace = { workspace = true }
alloy-sol-types       = { workspace = true }
anyhow                = { workspace = true }
csv                   = { workspace = true }
dotenv                = { workspace = true }
futures-util          = { workspace = true }
serde                 = { workspace = true }
serde_json            = { workspace = true }
shared                = { workspace = true }
simulator             = { workspace = true }
tx_sender             = { workspace = true }
tokio                 = { workspace = true }
tracing               = { workspace = true }
revm                  = { workspace = true }
pool_data             = { workspace = true }
