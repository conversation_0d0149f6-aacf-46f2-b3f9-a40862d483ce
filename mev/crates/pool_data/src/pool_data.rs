use std::collections::HashMap;
use once_cell::sync::Lazy;
use alloy_primitives::Address;

// A static map of pool address -> (token0, token1)
pub static POOL_TOKENS: Lazy<HashMap<Address, (Address, Address)>> = Lazy::new(|| {
    let mut map = HashMap::new();

    map.insert(
        "0xac7aEfD408E78897Dc75F6a594Fc17f7a5D8D064" // Hyperswap HYPE/stHYPE pool
            .parse()
            .unwrap(),
        (
            "0x5555555555555555555555555555555555555555" // WHYPE
            .parse()
            .unwrap(),
            "0xfFaa4a3D97fE9107Cef8a3F48c069F577Ff76cC1" // stHYPE
            .parse()
            .unwrap(),
        ),
    );


    map
});
