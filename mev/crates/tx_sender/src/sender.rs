use alloy::primitives::{<PERSON><PERSON>, Bytes, TxKind, U256};
use alloy::providers::Provider;
use alloy::signers::local::PrivateKeySigner;
use alloy::transports::http::Http;
use alloy::network::EthereumWallet;
use alloy_network::{AnyNetwork, Ethereum, NetworkWallet, ReceiptResponse, TransactionBuilder};
use alloy_provider::{ProviderBuilder, RootProvider};
use alloy_rpc_client::ClientBuilder;
use alloy_rpc_types::serde_helpers::WithOtherFields;
use alloy_rpc_types::{TransactionRequest};
use alloy_sol_types::SolCall;
use anyhow::{anyhow, Result};
use shared::utils::get_env;
use simulator::abi;
use std::time::Duration;
use tokio::time::sleep;
use tracing::{error, info};
use std::env;
use dotenv::dotenv;

/// Configuration for blockchain connection
struct BlockchainConfig {
    rpc_url: String,
    chain_id: u64,
    private_key: String,
}

/// Handles blockchain transactions
struct TransactionSender {
    provider:
        alloy_provider::fillers::FillProvider<alloy_provider::fillers::JoinFill<alloy_provider::Identity, alloy_provider::fillers::WalletFiller<EthereumWallet>>, RootProvider<Http<alloy_transport_http::Client>, AnyNetwork>, Http<alloy_transport_http::Client>, AnyNetwork> ,
    chain_id: u64,
    wallet: EthereumWallet
}

impl TransactionSender {
    /// Creates a new transaction sender with the given configuration
    fn new(config: BlockchainConfig) -> Result<Self> {
        dotenv().ok();

        let private_key_str = env::var("PRIVATE_KEY").expect("PRIVATE_KEY must be set in .env");

        let pk_signer: PrivateKeySigner = private_key_str.parse()?;

        let wallet = EthereumWallet::new(pk_signer); // https://alloy.rs/contract-interactions/write-contract https://alloy.rs/guides/signers-vs-ethereum-wallet

        // Create HTTP provider
        let provider= ProviderBuilder::new()
            .network::<alloy_network::AnyNetwork>()
            .wallet(wallet.clone())
            .on_client(ClientBuilder::default().http(config.rpc_url.parse()?));

        Ok(Self { provider, wallet, chain_id: config.chain_id })
    }

    /// Returns the sender's address
    fn address(&self) -> Address {
        NetworkWallet::<AnyNetwork>::default_signer_address(&self.wallet)
    }

    /// Calls a contract with the given parameters
    async fn call_contract(
        &self,
        data: Bytes,
        value: U256,
        max_fee_per_gas: u128,
        max_priority_fee_per_gas: u128,
    ) -> Result<String> {
        // Get gas price (or use EIP-1559 fields)
        let gas_price = self.provider.get_gas_price().await?;

        // Get the current nonce for the sender address
        let nonce = self.provider.get_transaction_count(self.address()).await?;

        // Get target contract address
        let contract_address = get_env("SIMULATOR_ADDRESS").parse::<Address>()?;
        // info!("Simulator contract address: {}", contract_address);

        // Create transaction request
        let tx = TransactionRequest { // https://alloy.rs/transactions/sending-a-legacy-transaction https://alloy.rs/transactions/introduction
            from: Some(self.address()),
            to: Some(TxKind::Call(contract_address)),
            max_fee_per_gas: Some(max_fee_per_gas),
            max_priority_fee_per_gas: Some(max_priority_fee_per_gas),
            gas: Some(1_000_000u64),
            gas_price: Some(gas_price),
            value: Some(value),
            input: data.into(),
            nonce: Some(nonce), // Add the nonce here
            ..Default::default()
        };

        let tx_wrapped = WithOtherFields::new(tx);
        // info!("Transaction send before");
        let tx_hash = self.provider.send_transaction(tx_wrapped).await?.watch().await?;
        // info!("Transaction send after");

        Ok(format!("{:?}", tx_hash))
    }

    /// Waits for a transaction to be mined and returns whether it was successful
    async fn wait_for_transaction(&self, tx_hash: &str) -> Result<bool> {
        let tx_hash = tx_hash.parse()?;

        // Poll for transaction receipt
        for attempt in 1..=60 {
            // Try for 60 attempts (5 minutes with 5s sleep)
            match self.provider.get_transaction_receipt(tx_hash).await {
                Ok(Some(receipt)) => {
                    if receipt.status() {
                        info!("Transaction succeeded on attempt {}", attempt);
                        return Ok(true);
                    } else {
                        error!("Transaction failed - status: false");
                        return Ok(false);
                    }
                }
                Ok(None) => {
                    // Transaction not yet mined, wait and retry
                    info!("Waiting for transaction to be mined (attempt {}/60)...", attempt);
                    sleep(Duration::from_secs(5)).await;
                }
                Err(e) => {
                    // Error occurred, wait and retry
                    error!("Error getting receipt: {}", e);
                    sleep(Duration::from_secs(5)).await;
                }
            }
        }

        Err(anyhow!("Transaction not mined after timeout"))
    }
}

/// Runs the flashloan LST arbitrage transaction with the provided parameters.
pub async fn run_flashloan_lst_arbitrage(
    lst_address: Address,
    token_in_address: Address,
    amount_in: U256,
    sell_data: Bytes,
    max_fee_per_gas: u128,
    max_priority_fee_per_gas: u128,
) -> Result<()> {
    // Load environment variables
    let config = BlockchainConfig {
        rpc_url: get_env("RPC_HTTPS_URL"),
        chain_id: get_env("CHAIN_ID").parse().unwrap(),
        private_key: get_env("PRIVATE_KEY"),
    };

    let sender = TransactionSender::new(config)?;
    info!("Sender address: {}", sender.address());

    // Construct calldata
    let call_data = abi::Simulator::flashloanLstArbitrageCall::new((
        lst_address,
        token_in_address,
        amount_in,
        sell_data,
    ))
    .abi_encode();

    let tx_hash = sender
        .call_contract(
            Bytes::from(call_data),
            U256::ZERO,
            max_fee_per_gas,
            max_priority_fee_per_gas,
        )
        .await?;

    info!("Transaction sent: {}", tx_hash);

    let success = sender.wait_for_transaction(&tx_hash).await?;

    if success {
        info!("Flashloan LST arbitrage executed successfully!");
    } else {
        error!("Flashloan LST arbitrage failed!");
    }

    Ok(())
}

/// Runs the flashswap arbitrage transaction with the provided parameters.
pub async fn run_flashswap_arbitrage(
    pool_address: Address,
    zero_for_one: bool,
    amount_in: U256,
    max_fee_per_gas: u128,
    max_priority_fee_per_gas: u128,
) -> Result<()> {
    // Load environment variables
    let config = BlockchainConfig {
        rpc_url: get_env("RPC_HTTPS_URL"),
        chain_id: get_env("CHAIN_ID").parse().unwrap(),
        private_key: get_env("PRIVATE_KEY"),
    };

    let sender = TransactionSender::new(config)?;
    info!("Sender address: {}", sender.address());

    // Construct calldata
    let call_data =
        abi::Simulator::flashswapLstArbitrageV2Call::new((pool_address, zero_for_one, amount_in))
            .abi_encode();

    let tx_hash = sender
        .call_contract(
            Bytes::from(call_data),
            U256::ZERO,
            max_fee_per_gas,
            max_priority_fee_per_gas,
        )
        .await?;

    info!("Transaction sent: {}", tx_hash);

    let success = sender.wait_for_transaction(&tx_hash).await?;

    if success {
        info!("Flashswap arbitrage executed successfully!");
    } else {
        error!("Flashswap arbitrage failed!");
    }

    Ok(())
}
