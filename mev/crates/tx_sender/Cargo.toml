[package]
name = "tx_sender"
version = "0.1.0"
edition = "2021"

[dependencies]
alloy.workspace = true
alloy-network.workspace = true
alloy-provider.workspace = true
alloy-rpc-client.workspace = true
alloy-rpc-types.workspace = true
alloy-rpc-types-eth.workspace = true
alloy-sol-types.workspace = true
alloy-transport-http.workspace = true
anyhow = "1.0"
clap = { version = "4.4", features = ["derive"] }
dotenv = "0.15"
shared = { path = "../../crates/shared" }
simulator = { path = "../../crates/simulator" }
tokio = { version = "1.32", features = ["full"] }
tracing = "0.1"
tracing-subscriber = "0.3"
